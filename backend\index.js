const express = require('express');
const { Payment, Callback } = require('gtxpoint');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// GTXPoint configuration
const GTXPOINT_CONFIG = {
  path: 'https://paymentpage.eposservice.com',
  projectId: '143693',
  secretKey: 'b0e6e8b25aa6106d2e7313d49d1771917a0541f9e81bf670a3cf37655b23c7dc594a65ae6b05bc6e7f829497a8f677e61a6d0a4ec219444b409b5aade34137c1'
};

// Routes

/**
 * POST /api/payment/create
 * Create a new payment URL
 */
app.post('/api/payment/create', (req, res) => {
  try {
    const {
      paymentAmount,
      paymentId,
      paymentCurrency = 'USD',
      customerId,
      paymentDescription,
      customerEmail,
      customerFirstName,
      customerLastName,
      customerPhone,
      merchantSuccessUrl,
      merchantFailUrl
    } = req.body;

    // Validate required fields
    if (!paymentAmount || !paymentId || !customerId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: paymentAmount, paymentId, customerId'
      });
    }

    // Create payment instance
    const payment = new Payment(
      GTXPOINT_CONFIG.path,
      GTXPOINT_CONFIG.projectId,
      GTXPOINT_CONFIG.secretKey
    );

    // Set payment parameters
    payment.paymentAmount = paymentAmount;
    payment.paymentId = paymentId;
    payment.paymentCurrency = paymentCurrency;
    payment.customerId = customerId;
    payment.paymentDescription = paymentDescription || `Payment for order ${paymentId}`;

    // Set optional customer details
    if (customerEmail) payment.customerEmail = customerEmail;
    if (customerFirstName) payment.customerFirstName = customerFirstName;
    if (customerLastName) payment.customerLastName = customerLastName;
    if (customerPhone) payment.customerPhone = customerPhone;

    // Set callback URLs
    if (merchantSuccessUrl) payment.merchantSuccessUrl = merchantSuccessUrl;
    if (merchantFailUrl) payment.merchantFailUrl = merchantFailUrl;

    // Generate payment URL
    const paymentUrl = payment.getUrl();

    res.json({
      success: true,
      paymentUrl: paymentUrl,
      paymentId: paymentId,
      amount: paymentAmount,
      currency: paymentCurrency
    });

  } catch (error) {
    console.error('Error creating payment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create payment URL',
      message: error.message
    });
  }
});

/**
 * POST /api/payment/callback
 * Handle payment callback from GTXPoint
 */
app.post('/api/payment/callback', (req, res) => {
  try {
    console.log('Received payment callback:', req.body);

    const result = handleCallback(req.body);

    if (result.success) {
      console.log(`Payment successful for ID: ${result.paymentId}`);

      // Here you can add your business logic for successful payments
      // For example: update database, send confirmation email, etc.

      res.status(200).json({
        success: true,
        message: 'Callback processed successfully'
      });
    } else {
      console.log(`Payment failed or pending for ID: ${result.paymentId}`);

      res.status(200).json({
        success: false,
        message: result.message
      });
    }

  } catch (error) {
    console.error('Error processing callback:', error);
    res.status(400).json({
      success: false,
      error: 'Failed to process callback',
      message: error.message
    });
  }
});

/**
 * GET /api/payment/status/:paymentId
 * Get payment status (placeholder for future implementation)
 */
app.get('/api/payment/status/:paymentId', (req, res) => {
  const { paymentId } = req.params;

  // This is a placeholder - in a real application, you would
  // query your database for the payment status
  res.json({
    success: true,
    paymentId: paymentId,
    message: 'Payment status endpoint - implement database query here'
  });
});

/**
 * Handle payment callback from GTXPoint
 * @param {Object} callbackData - The callback data received from GTXPoint
 * @returns {Object} - Object containing success status and payment details
 */
function handleCallback(callbackData) {
  try {
    // Create callback instance with secret key and received data
    const callback = new Callback(GTXPOINT_CONFIG.secretKey, callbackData);

    // Check if payment was successful
    if (callback.isPaymentSuccess()) {
      const paymentId = callback.getPaymentId();

      console.log('Payment successful!');
      console.log('Payment ID:', paymentId);

      // Return success response
      return {
        success: true,
        paymentId: paymentId,
        message: 'Payment processed successfully',
        callbackData: callback.callback
      };
    } else {
      console.log('Payment failed or pending');

      // Return failure response
      return {
        success: false,
        paymentId: callback.getPaymentId(),
        message: 'Payment failed or is pending',
        callbackData: callback.callback
      };
    }
  } catch (error) {
    console.error('Error processing callback:', error.message);

    // Return error response
    return {
      success: false,
      error: error.message,
      message: 'Invalid callback signature or malformed data'
    };
  }
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'EPOS Service is running',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`EPOS Service running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Payment creation: POST http://localhost:${PORT}/api/payment/create`);
  console.log(`Payment callback: POST http://localhost:${PORT}/api/payment/callback`);
});

// Export the app and function for testing
module.exports = {
  app,
  handleCallback
};