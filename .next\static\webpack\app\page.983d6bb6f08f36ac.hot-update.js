"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PaymentConfigForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PaymentConfigForm */ \"(app-pages-browser)/./components/PaymentConfigForm.tsx\");\n/* harmony import */ var _components_PaymentIframe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PaymentIframe */ \"(app-pages-browser)/./components/PaymentIframe.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [paymentResponse, setPaymentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleFormSubmit = async (config)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.createPayment)(config);\n            if (response.success) {\n                setPaymentResponse(response);\n            } else {\n                setError(response.error || 'Failed to create payment URL');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An unexpected error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleReset = ()=>{\n        setPaymentResponse(null);\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 sm:text-4xl\",\n                        children: \"Payment Configuration Portal\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg text-gray-600 max-w-2xl mx-auto\",\n                        children: \"Configure your payment parameters and generate a secure payment URL. The payment page will be displayed in the iframe below for easy testing and integration.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error creating payment URL\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        className: \"text-sm bg-red-100 text-red-800 px-3 py-1 rounded-md hover:bg-red-200 transition-colors\",\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this),\n            (paymentResponse === null || paymentResponse === void 0 ? void 0 : paymentResponse.success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-md p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Payment URL generated successfully!\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Payment ID: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-green-100 px-1 rounded\",\n                                                    children: paymentResponse.paymentId\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 32\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Amount: \",\n                                                paymentResponse.amount,\n                                                \" \",\n                                                paymentResponse.currency\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        className: \"text-sm bg-green-100 text-green-800 px-3 py-1 rounded-md hover:bg-green-200 transition-colors mr-2\",\n                                        children: \"Create New Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PaymentConfigForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onSubmit: handleFormSubmit,\n                            loading: isLoading\n                        }, void 0, false, {\n                            fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PaymentIframe__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                paymentUrl: (paymentResponse === null || paymentResponse === void 0 ? void 0 : paymentResponse.paymentUrl) || null,\n                                paymentId: paymentResponse === null || paymentResponse === void 0 ? void 0 : paymentResponse.paymentId,\n                                amount: paymentResponse === null || paymentResponse === void 0 ? void 0 : paymentResponse.amount,\n                                currency: paymentResponse === null || paymentResponse === void 0 ? void 0 : paymentResponse.currency,\n                                onClose: handleReset\n                            }, void 0, false, {\n                                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            (paymentResponse === null || paymentResponse === void 0 ? void 0 : paymentResponse.paymentUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 border border-gray-200 rounded-md p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-800 mb-2\",\n                                        children: \"API Response\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs text-gray-600 bg-white p-3 rounded border overflow-x-auto\",\n                                        children: JSON.stringify(paymentResponse, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\epos_service_ui_form\\\\app\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"mhSrjbXKVPzdLpoJk5qXIWe1HI4=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});