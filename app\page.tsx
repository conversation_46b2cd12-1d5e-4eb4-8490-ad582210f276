'use client';

import { useState } from 'react';
import PaymentConfigForm from '@/components/PaymentConfigForm';
import PaymentIframe from '@/components/PaymentIframe';
import { PaymentConfig, PaymentResponse } from '@/types/payment';
import { createPayment } from '@/lib/api';

export default function HomePage() {
  const [paymentResponse, setPaymentResponse] = useState<PaymentResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFormSubmit = async (config: PaymentConfig) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await createPayment(config);
      
      if (response.success) {
        setPaymentResponse(response);
      } else {
        setError(response.error || 'Failed to create payment URL');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setPaymentResponse(null);
    setError(null);
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
          Payment Configuration Portal
        </h2>
        <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
          Configure your payment parameters and generate a secure payment URL. 
          The payment page will be displayed in the iframe below for easy testing and integration.
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error creating payment URL
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={handleReset}
                  className="text-sm bg-red-100 text-red-800 px-3 py-1 rounded-md hover:bg-red-200 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Display */}
      {paymentResponse?.success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                Payment URL generated successfully!
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <p>Payment ID: <code className="bg-green-100 px-1 rounded">{paymentResponse.paymentId}</code></p>
                <p>Amount: {paymentResponse.amount} {paymentResponse.currency}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={handleReset}
                  className="text-sm bg-green-100 text-green-800 px-3 py-1 rounded-md hover:bg-green-200 transition-colors mr-2"
                >
                  Create New Payment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Form */}
        <div className="space-y-6">
          <PaymentConfigForm 
            onSubmit={handleFormSubmit} 
            loading={isLoading}
          />
          
          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Instructions</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Fill in the required fields: Amount, Currency, and Customer ID</li>
              <li>• Optional fields can be used for better customer experience</li>
              <li>• Click "Generate Payment URL" to create the payment page</li>
              <li>• The payment page will appear in the iframe on the right</li>
              <li>• You can copy the URL or open it in a new tab for testing</li>
            </ul>
          </div>
        </div>

        {/* Right Column - Iframe */}
        <div className="space-y-6">
          <PaymentIframe
            paymentUrl={paymentResponse?.paymentUrl || null}
            paymentId={paymentResponse?.paymentId}
            amount={paymentResponse?.amount}
            currency={paymentResponse?.currency}
            onClose={handleReset}
          />
          
          {/* API Information */}
          {paymentResponse?.paymentUrl && (
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-gray-800 mb-2">API Response</h3>
              <pre className="text-xs text-gray-600 bg-white p-3 rounded border overflow-x-auto">
                {JSON.stringify(paymentResponse, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>

      {/* Additional Information */}
      {/* <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">About This Portal</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Secure Payments</h4>
            <p className="text-sm text-gray-600">
              All payments are processed securely through GTXPoint's payment gateway with industry-standard encryption.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Real-time Testing</h4>
            <p className="text-sm text-gray-600">
              Test your payment configurations in real-time with the embedded iframe display.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Easy Integration</h4>
            <p className="text-sm text-gray-600">
              Copy the generated URLs and integrate them directly into your applications or websites.
            </p>
          </div>
        </div>
      </div> */}
    </div>
  );
}
